<template>
  <div class="apple-dashboard">
    <!-- 苹果风格侧边栏 -->
    <div class="apple-sidebar" :class="{ 'collapsed': isCollapse }">
      <!-- Logo区域 -->
      <div class="apple-logo">
        <div class="logo-icon">📚</div>
        <div class="logo-text" v-show="!isCollapse">爱写作</div>
      </div>

      <!-- 导航菜单 -->
      <nav class="apple-nav">
        <div class="nav-section">
          <div class="nav-section-title" v-show="!isCollapse">主要功能</div>
          <div class="nav-items">
            <div
              v-for="item in mainMenuItems"
              :key="item.index"
              class="nav-item"
              :class="{ 'active': activeMenu === item.index }"
              @click="handleMenuSelect(item.index)"
            >
              <div class="nav-item-icon">
                <component :is="item.icon" />
              </div>
              <span class="nav-item-text" v-show="!isCollapse">{{ item.title }}</span>
            </div>
          </div>
        </div>

        <div class="nav-section">
          <div class="nav-section-title" v-show="!isCollapse">工具与设置</div>
          <div class="nav-items">
            <div
              v-for="item in toolMenuItems"
              :key="item.index"
              class="nav-item"
              :class="{ 'active': activeMenu === item.index }"
              @click="handleMenuSelect(item.index)"
            >
              <div class="nav-item-icon">
                <component :is="item.icon" />
              </div>
              <span class="nav-item-text" v-show="!isCollapse">{{ item.title }}</span>
            </div>
          </div>
        </div>
      </nav>
    </div>

    <!-- 主要内容区域 -->
    <div class="apple-main">
      <!-- 苹果风格顶部栏 -->
      <header class="apple-header">
        <div class="header-left">
          <button class="sidebar-toggle" @click="toggleSidebar">
            <Expand v-if="isCollapse" />
            <Fold v-else />
          </button>
          <h1 class="page-title">{{ pageTitle }}</h1>
        </div>

        <div class="header-center">
          <!-- 模型选择器 -->
          <div class="model-selector-wrapper" v-if="isApiConfigured">
            <div class="model-selector">
              <el-select
                v-model="currentModel"
                @change="handleModelChange"
                placeholder="选择模型"
                class="apple-select"
              >
                <el-option-group label="可用模型" v-if="availableModels.length > 0">
                  <el-option
                    v-for="model in availableModels"
                    :key="model.id"
                    :label="model.name"
                    :value="model.id"
                  >
                    <span>{{ model.name }}</span>
                    <span v-if="model.description" class="model-description">
                      {{ model.description }}
                    </span>
                  </el-option>
                </el-option-group>
                <el-option
                  v-if="availableModels.length === 0"
                  disabled
                  value=""
                  label="暂无可用模型，请先创建API配置"
                />
              </el-select>
            </div>
          </div>
        </div>

        <div class="header-right">
          <!-- API配置按钮 -->
          <button
            class="api-config-btn"
            :class="{ 'configured': isApiConfigured }"
            @click="showApiConfig = true"
          >
            <Key />
            <span>{{ isApiConfigured ? 'API已配置' : 'API配置' }}</span>
          </button>

          <!-- 用户信息 -->
          <el-dropdown @command="handleCommand" v-if="currentUser" class="user-dropdown-wrapper">
            <div class="user-profile">
              <div class="user-avatar">
                <el-avatar :size="36" :src="currentUser.avatar">
                  <User />
                </el-avatar>
              </div>
              <div class="user-info" v-show="!isCollapse">
                <div class="user-name">{{ currentUser.nickname || currentUser.username }}</div>
                <div class="user-status">在线</div>
              </div>
              <ArrowDown class="dropdown-arrow" />
            </div>
            <template #dropdown>
              <el-dropdown-menu class="apple-dropdown">
                <el-dropdown-item command="profile">
                  <User />
                  <span>个人中心</span>
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <Setting />
                  <span>个人设置</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <SwitchButton />
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="apple-content">
        <router-view />
      </main>
    </div>

    <!-- API配置对话框 -->
    <el-dialog
      v-model="showApiConfig"
      title="API配置"
      width="1000px"
      class="apple-dialog"
    >
      <ApiConfig @close="showApiConfig = false" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNovelStore } from '@/stores/novel'
import {
  House, Document, ChatLineSquare, Collection, Notebook, Aim,
  CreditCard, Setting, Key, Tools, EditPen, DataAnalysis,
  Expand, Fold, User, SwitchButton, ArrowDown
} from '@element-plus/icons-vue'
import ApiConfig from '@/components/ApiConfig.vue'
import { authApi, authUtils } from '@/services/authApi'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const route = useRoute()
const novelStore = useNovelStore()

// 响应式数据
const isCollapse = ref(false)
const showApiConfig = ref(false)
const activeMenu = ref('/')
const currentModel = ref('')
const currentUser = ref(null)

// 菜单项数据
const mainMenuItems = ref([
  { index: '/', title: '首页', icon: House },
  { index: '/novels', title: '小说列表', icon: Document },
  { index: '/prompts', title: '提示词库', icon: ChatLineSquare },
  { index: '/genres', title: '小说类型管理', icon: Collection },
  { index: '/chapters', title: '章节管理', icon: Notebook },
  { index: '/goals', title: '写作目标', icon: Aim },
  { index: '/billing', title: 'Token计费', icon: CreditCard }
])

const toolMenuItems = ref([
  { index: '/tools', title: '工具库', icon: Tools },
  { index: '/short-story', title: '短文写作', icon: EditPen },
  { index: '/book-analysis', title: '拆书工具', icon: DataAnalysis },
  { index: '/settings', title: '系统设置', icon: Setting }
])

// 计算属性
const isApiConfigured = computed(() => novelStore.isApiConfigured)

// 获取当前API配置
const currentApiConfig = computed(() => {
  return novelStore.getCurrentApiConfig()
})



// 可用模型列表（从后端API配置中获取）
const availableModels = ref([])

// 加载可用模型列表
const loadAvailableModels = async () => {
  try {
    // 检查用户是否已登录
    const token = localStorage.getItem('token')
    if (!token) {
      console.log('用户未登录，无法加载后端模型配置')
      availableModels.value = []
      return
    }

    // 从后端获取所有启用的API配置
    const { apiConfigApi } = await import('@/services/apiConfigApi.js')
    const configs = await apiConfigApi.getApiConfigs()

    if (!configs || configs.length === 0) {
      console.log('暂无API配置')
      availableModels.value = []
      return
    }

    const enabledConfigs = configs.filter(config => config.enabled && config.selectedModel)

    // 提取所有可用的模型
    const modelSet = new Set()
    const modelDetails = new Map()

    // 只添加配置中的模型
    enabledConfigs.forEach(config => {
      if (config.selectedModel) {
        modelSet.add(config.selectedModel)
        modelDetails.set(config.selectedModel, {
          id: config.selectedModel,
          name: config.selectedModel,
          description: `来自配置: ${config.name}`
        })
      }
    })

    availableModels.value = Array.from(modelSet).map(id => modelDetails.get(id))
    console.log('加载的可用模型:', availableModels.value)
  } catch (error) {
    console.error('加载可用模型失败:', error)
    // 如果加载失败，设置为空数组
    availableModels.value = []
  }
}



const pageTitle = computed(() => {
  const titleMap = {
    '/': '首页',
    '/novels': '小说列表',
    '/prompts': '提示词库',
    '/genres': '小说类型管理',
    '/chapters': '章节管理',
    '/goals': '写作目标',
    '/billing': 'Token计费',
    '/tools': '工具库',
    '/short-story': '短文写作',
    '/book-analysis': '拆书工具',
    '/settings': '系统设置'
  }
  return titleMap[route.path] || '首页'
})



// 方法
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const handleMenuSelect = (index) => {
  router.push(index)
}

// 用户相关功能
const loadUserInfo = () => {
  try {
    const user = authUtils.getUser()
    if (user) {
      currentUser.value = user
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}





// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      handleProfile()
      break
    case 'settings':
      handleSettings()
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleProfile = () => {
  // 跳转到个人中心页面
  router.push('/profile')
}

const handleSettings = () => {
  // 跳转到个人设置页面
  router.push('/settings')
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '系统提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'logout-confirm-dialog'
      }
    )

    // 显示加载状态
    const loading = ElMessage({
      message: '正在退出...',
      type: 'info',
      duration: 0
    })

    try {
      // 调用注销API
      await authApi.logout()

      // 清除用户信息
      currentUser.value = null

      loading.close()
      ElMessage.success('退出成功')

      // 跳转到登录页面
      router.push('/login')

    } catch (apiError) {
      loading.close()
      console.error('注销API调用失败:', apiError)

      // 即使API调用失败，也要清除本地数据
      authApi.logout() // 这个方法只清除本地存储
      currentUser.value = null

      ElMessage.success('退出成功')
      router.push('/login')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
      ElMessage.error('退出登录失败')
    }
  }
}

// 模型相关功能
const handleModelChange = async (modelId) => {
  try {
    console.log('切换模型:', modelId)

    // 查找包含此模型的API配置
    const { apiConfigApi } = await import('@/services/apiConfigApi.js')
    const configs = await apiConfigApi.getApiConfigs()
    const configWithModel = configs.find(config =>
      config.selectedModel === modelId
    )

    if (configWithModel) {
      // 1. 首先确保配置是启用状态
      if (!configWithModel.enabled) {
        const enableConfigData = {
          ...configWithModel,
          enabled: 1
        }
        await apiConfigApi.updateApiConfig(configWithModel.id, enableConfigData)
        console.log('已启用配置:', configWithModel.name)
      }

      // 2. 设置为默认配置
      await apiConfigApi.setDefaultApiConfig(configWithModel.id)
      console.log('已设为默认配置:', configWithModel.name)

      // 3. 切换到此配置
      const success = await novelStore.switchToApiConfig(configWithModel.id)

      if (success) {
        currentModel.value = modelId
        const modelName = getModelDisplayName(modelId)
        ElMessage.success(`已切换到模型: ${modelName}，并设为默认配置`)
      } else {
        ElMessage.error('配置设置成功，但切换失败')
      }
    } else {
      // 如果没有找到对应的配置，提示用户创建配置
      const modelName = getModelDisplayName(modelId)
      ElMessage.warning(`模型 ${modelName} 需要先创建对应的API配置`)
    }

  } catch (error) {
    console.error('切换模型失败:', error)
    ElMessage.error('切换模型失败: ' + error.message)
  }
}

const getModelDisplayName = (modelId) => {
  // 在可用模型中查找
  const model = availableModels.value.find(m => m.id === modelId)
  if (model) return model.name

  // 找不到就返回原ID
  return modelId
}

// 初始化模型选择器
const initializeModelSelector = async () => {
  try {
    // 加载可用模型列表
    await loadAvailableModels()

    // 获取当前选中的模型
    try {
      // 直接从API服务获取当前配置
      const { apiConfigApi } = await import('@/services/apiConfigApi.js')
      const defaultConfig = await apiConfigApi.getDefaultApiConfig()

      if (defaultConfig && defaultConfig.selectedModel) {
        currentModel.value = defaultConfig.selectedModel
        console.log('从默认配置获取到模型:', currentModel.value)
      } else if (currentApiConfig.value && currentApiConfig.value.selectedModel) {
        // 回退到计算属性
        currentModel.value = currentApiConfig.value.selectedModel
        console.log('从计算属性获取到模型:', currentModel.value)
      } else {
        console.log('未找到默认模型')
      }
    } catch (error) {
      console.error('获取默认配置失败:', error)
      // 回退到计算属性
      if (isApiConfigured.value && currentApiConfig.value) {
        currentModel.value = currentApiConfig.value.selectedModel || ''
      }
    }

    console.log('模型选择器初始化完成, 当前模型:', currentModel.value)
  } catch (error) {
    console.error('初始化模型选择器失败:', error)
  }
}

// 监听路由变化
watch(() => route.path, (newPath) => {
  activeMenu.value = newPath
}, { immediate: true })

// 监听API配置变化，更新模型选择器
watch(() => [isApiConfigured.value, currentApiConfig.value], async () => {
  // 延迟一下，确保配置已经加载完成
  setTimeout(async () => {
    await initializeModelSelector()
  }, 100)
}, { immediate: true })

// 组件挂载时初始化
onMounted(async () => {
  // 确保store已经初始化完成
  await novelStore.initializeApiConfig()

  // 延迟一下再初始化模型选择器
  setTimeout(async () => {
    await initializeModelSelector()
  }, 200)

  loadUserInfo()
})

</script>

<style scoped>
/* 苹果风格全局样式 */
.apple-dashboard {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

/* 苹果风格侧边栏 */
.apple-sidebar {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

.apple-sidebar.collapsed {
  width: 80px;
}

.apple-sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 12px;
}

.apple-sidebar.collapsed .nav-item-icon {
  margin-right: 0;
}

.apple-sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 12px;
}

.apple-sidebar.collapsed .nav-item-icon {
  margin-right: 0;
}

/* Logo区域 */
.apple-logo {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.8);
}

.logo-icon {
  font-size: 28px;
  margin-right: 12px;
  transition: all 0.3s ease;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  letter-spacing: -0.5px;
  transition: all 0.3s ease;
}

.apple-sidebar.collapsed .logo-text {
  opacity: 0;
  transform: translateX(-20px);
}

/* 导航区域 */
.apple-nav {
  flex: 1;
  padding: 24px 16px;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 32px;
}

.nav-section-title {
  font-size: 13px;
  font-weight: 600;
  color: #86868b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
  padding: 0 12px;
  transition: all 0.3s ease;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
}

.nav-item:hover {
  background: rgba(0, 122, 255, 0.1);
  transform: translateX(4px);
}

.nav-item.active {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
}

.nav-item-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  transition: all 0.3s ease;
}

.nav-item-text {
  font-size: 15px;
  font-weight: 500;
  color: #1d1d1f;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.nav-item.active .nav-item-text {
  color: white;
}

.apple-sidebar.collapsed .nav-item-text {
  opacity: 0;
  transform: translateX(-20px);
}

.apple-sidebar.collapsed .nav-section-title {
  opacity: 0;
}

/* 主要内容区域 */
.apple-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px 0 0 0;
  margin-left: -20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 苹果风格顶部栏 */
.apple-header {
  height: 80px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  position: relative;
  z-index: 50;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.sidebar-toggle {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #007aff;
  font-size: 16px;
}

.sidebar-toggle:hover {
  background: rgba(0, 122, 255, 0.2);
  transform: scale(1.05);
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0;
  letter-spacing: -0.5px;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 400px;
}

.model-selector-wrapper {
  width: 100%;
}

.model-selector {
  width: 100%;
}

.apple-select {
  width: 100%;
}

.apple-select :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.apple-select :deep(.el-input__wrapper:hover) {
  border-color: #007aff;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.2);
}

.model-description {
  float: right;
  color: #86868b;
  font-size: 12px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* API配置按钮 */
.api-config-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 149, 0, 0.1);
  color: #ff9500;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.api-config-btn.configured {
  background: rgba(52, 199, 89, 0.1);
  color: #34c759;
}

.api-config-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 用户信息 */
.user-dropdown-wrapper {
  position: relative;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.user-profile:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.user-avatar {
  position: relative;
}

.user-avatar::after {
  content: '';
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 10px;
  height: 10px;
  background: #34c759;
  border: 2px solid white;
  border-radius: 50%;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  line-height: 1.2;
}

.user-status {
  font-size: 12px;
  color: #34c759;
  line-height: 1.2;
}

.dropdown-arrow {
  font-size: 12px;
  color: #86868b;
  transition: transform 0.3s ease;
}

.user-profile:hover .dropdown-arrow {
  transform: rotate(180deg);
}

/* 下拉菜单样式 */
.apple-dropdown {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 8px;
}

.apple-dropdown :deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  transition: all 0.3s ease;
}

.apple-dropdown :deep(.el-dropdown-menu__item:hover) {
  background: rgba(0, 122, 255, 0.1);
  color: #007aff;
}

/* 主内容区域 */
.apple-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 20px 20px 0 0;
  margin-top: -20px;
  padding-top: 52px;
}

/* 对话框样式 */
.apple-dialog :deep(.el-dialog) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .apple-sidebar {
    width: 240px;
  }

  .apple-sidebar.collapsed {
    width: 70px;
  }

  .apple-header {
    padding: 0 24px;
  }

  .apple-content {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .apple-sidebar {
    position: fixed;
    z-index: 1000;
    height: 100vh;
    width: 280px;
    transform: translateX(-100%);
  }

  .apple-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .apple-main {
    margin-left: 0;
    border-radius: 0;
  }

  .header-center {
    display: none;
  }

  .apple-content {
    padding: 20px;
    margin-top: 0;
    border-radius: 0;
  }
}

/* 滚动条样式 */
.apple-nav::-webkit-scrollbar,
.apple-content::-webkit-scrollbar {
  width: 6px;
}

.apple-nav::-webkit-scrollbar-track,
.apple-content::-webkit-scrollbar-track {
  background: transparent;
}

.apple-nav::-webkit-scrollbar-thumb,
.apple-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.apple-nav::-webkit-scrollbar-thumb:hover,
.apple-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>