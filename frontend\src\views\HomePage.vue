<template>
  <div class="apple-home-page">
    <!-- 苹果风格欢迎区域 -->
    <div class="apple-hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">欢迎回来</h1>
          <p class="hero-subtitle">继续您的创作之旅，让AI助力您的小说创作</p>
        </div>
        <div class="hero-action">
          <button class="apple-primary-button" @click="createNovel">
            <span class="button-icon">✨</span>
            <span>创建新小说</span>
          </button>
        </div>
      </div>
      <div class="hero-decoration">
        <div class="floating-element element-1">📚</div>
        <div class="floating-element element-2">✍️</div>
        <div class="floating-element element-3">💡</div>
      </div>
    </div>

    <!-- 苹果风格统计卡片 -->
    <div class="apple-stats-section">
      <div class="stats-grid">
        <div class="apple-stat-card novels-card">
          <div class="stat-icon-wrapper">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-number">
              <span v-if="!isLoading">{{ stats.totalNovels }}</span>
              <div v-else class="stat-skeleton"></div>
            </div>
            <div class="stat-label">总小说数</div>
          </div>
        </div>

        <div class="apple-stat-card words-card">
          <div class="stat-icon-wrapper">
            <div class="stat-icon">
              <el-icon><EditPen /></el-icon>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-number">
              <span v-if="!isLoading">{{ formatNumber(stats.totalWords) }}</span>
              <div v-else class="stat-skeleton"></div>
            </div>
            <div class="stat-label">总字数</div>
          </div>
        </div>

        <div class="apple-stat-card chapters-card">
          <div class="stat-icon-wrapper">
            <div class="stat-icon">
              <el-icon><Notebook /></el-icon>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-number">
              <span v-if="!isLoading">{{ stats.totalChapters }}</span>
              <div v-else class="stat-skeleton"></div>
            </div>
            <div class="stat-label">总章节数</div>
          </div>
        </div>

        <div class="apple-stat-card tokens-card">
          <div class="stat-icon-wrapper">
            <div class="stat-icon">
              <el-icon><CreditCard /></el-icon>
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(stats.totalTokens) }}</div>
            <div class="stat-label">已用Token</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 苹果风格主要内容区域 -->
    <div class="apple-main-content">
      <!-- 左侧：写作目标 -->
      <div class="apple-goals-section">
        <div class="apple-card goals-card">
          <div class="card-header">
            <div class="header-left">
              <div class="header-icon">🎯</div>
              <h3 class="header-title">今日写作目标</h3>
            </div>
            <button class="apple-text-button" @click="showGoalsDialog = true">
              管理目标
            </button>
          </div>

          <div class="goals-content">
            <!-- 动态显示目标 -->
            <div
              v-for="goal in displayedGoals"
              :key="goal.id"
              class="apple-goal-item"
            >
              <div class="goal-header">
                <span class="goal-title">{{ goal.title }}</span>
                <span class="goal-target">{{ goal.targetValue }}{{ goal.unit }}</span>
              </div>
              <div class="goal-progress-wrapper">
                <div class="apple-progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: getGoalProgress(goal) + '%' }"
                    :class="getProgressClass(getGoalProgress(goal))"
                  ></div>
                </div>
                <span class="progress-text">{{ goal.currentValue }}{{ goal.unit }} / {{ goal.targetValue }}{{ goal.unit }}</span>
              </div>
            </div>

            <!-- 如果没有目标时显示默认内容 -->
            <div v-if="displayedGoals.length === 0" class="apple-empty-state">
              <div class="empty-icon">🎯</div>
              <p class="empty-text">暂无活跃目标</p>
              <button class="apple-secondary-button" @click="showGoalsDialog = true">
                创建目标
              </button>
            </div>

            <!-- 查看全部目标按钮 -->
            <div v-if="totalActiveGoals > maxDisplayGoals" class="view-all-goals">
              <button class="apple-link-button" @click="showGoalsDialog = true">
                查看全部 {{ totalActiveGoals }} 个目标 →
              </button>
            </div>

            <div class="streak-info" v-if="displayedGoals.length > 0">
              <div class="streak-icon">🏆</div>
              <span class="streak-text">连续写作 {{ calculateStreak() }} 天</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：快速操作 -->
      <div class="apple-actions-section">
        <div class="apple-card actions-card">
          <div class="card-header">
            <div class="header-left">
              <div class="header-icon">🚀</div>
              <h3 class="header-title">快速操作</h3>
            </div>
          </div>

          <div class="apple-actions-grid">
            <div class="apple-action-item" @click="openPrompts">
              <div class="action-icon-wrapper">
                <el-icon class="action-icon"><ChatLineSquare /></el-icon>
              </div>
              <span class="action-label">提示词库</span>
            </div>

            <div class="apple-action-item" @click="openChapters">
              <div class="action-icon-wrapper">
                <el-icon class="action-icon"><Notebook /></el-icon>
              </div>
              <span class="action-label">章节管理</span>
            </div>

            <div class="apple-action-item" @click="openBilling">
              <div class="action-icon-wrapper">
                <el-icon class="action-icon"><CreditCard /></el-icon>
              </div>
              <span class="action-label">Token计费</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 苹果风格最近小说 -->
    <div class="apple-novels-section">
      <div class="apple-card novels-card">
        <div class="card-header">
          <div class="header-left">
            <div class="header-icon">📚</div>
            <h3 class="header-title">最近编辑的小说</h3>
          </div>
          <button class="apple-text-button" @click="viewAllNovels">
            查看全部
          </button>
        </div>

        <div class="apple-novels-list">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="apple-loading-state">
            <div class="loading-skeleton" v-for="i in 3" :key="i">
              <div class="skeleton-cover"></div>
              <div class="skeleton-content">
                <div class="skeleton-title"></div>
                <div class="skeleton-desc"></div>
                <div class="skeleton-meta"></div>
              </div>
            </div>
          </div>

          <!-- 小说列表 -->
          <div
            v-else
            v-for="novel in recentNovels"
            :key="novel.id"
            class="apple-novel-item"
            @click="openNovel(novel)"
          >
            <div class="novel-cover-wrapper">
              <img v-if="novel.cover" :src="novel.cover" :alt="novel.title" class="novel-cover" />
              <div v-else class="default-novel-cover">
                <el-icon class="cover-icon"><Document /></el-icon>
              </div>
            </div>
            <div class="novel-content">
              <h4 class="apple-novel-title">{{ novel.title }}</h4>
              <p class="apple-novel-desc">{{ novel.description }}</p>
              <div class="apple-novel-meta">
                <span class="meta-item">
                  <span class="meta-icon">📝</span>
                  {{ formatNumber(novel.wordCount) }} 字
                </span>
                <span class="meta-item">
                  <span class="meta-icon">🕒</span>
                  {{ formatTime(novel.updatedAt) }}
                </span>
              </div>
            </div>
            <div class="novel-action">
              <button class="apple-continue-button">
                <span>继续写作</span>
                <span class="button-arrow">→</span>
              </button>
            </div>
          </div>

          <div v-if="!isLoading && recentNovels.length === 0" class="apple-empty-novels">
            <div class="empty-illustration">📖</div>
            <h4 class="empty-title">开始您的创作之旅</h4>
            <p class="empty-description">暂无小说，创作您的第一部作品吧！</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 写作目标管理对话框 -->
    <el-dialog v-model="showGoalsDialog" title="写作目标管理" width="800px">
      <WritingGoals @close="showGoalsDialog = false" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNovelStore } from '@/stores/novel'
import {
  Document, EditPen, Notebook, CreditCard, ChatLineSquare
} from '@element-plus/icons-vue'
import WritingGoals from '@/components/WritingGoals.vue'
import billingService from '@/services/billing.js'
import { novelApi } from '@/services/novelApi.js'

const router = useRouter()
const novelStore = useNovelStore()

// 响应式数据
const showGoalsDialog = ref(false)
const novelsData = ref([])
const isLoading = ref(true)
const stats = computed(() => {
  // 使用计费服务获取真实的token使用统计
  const usageStats = billingService.getUsageStats()

  // 计算真实统计数据
  const totalNovels = novelsData.value.length
  const totalWords = novelsData.value.reduce((sum, novel) => sum + (novel.wordCount || 0), 0)
  const totalChapters = novelsData.value.reduce((sum, novel) => sum + (novel.chapterCount || 0), 0)
  const totalTokens = usageStats.totalInputTokens + usageStats.totalOutputTokens

  return {
    totalNovels,
    totalWords,
    totalChapters,
    totalTokens
  }
})

// 添加响应式的目标数据状态
const goalsRefreshTrigger = ref(0)
const maxDisplayGoals = ref(3) // 首页最多显示的目标数量

// 获取所有活跃目标
const activeGoals = computed(() => {
  // 触发重新计算（通过依赖goalsRefreshTrigger）
  goalsRefreshTrigger.value
  
  // 从本地存储获取真实的写作目标数据
  const goalsData = JSON.parse(localStorage.getItem('writingGoals') || '[]')
  const active = goalsData.filter(goal => goal.status === 'active')
  
  // 按优先级排序（priority字段，数字越小优先级越高），如果没有priority则按创建时间排序
  return active.sort((a, b) => {
    if (a.priority !== undefined && b.priority !== undefined) {
      return a.priority - b.priority
    }
    if (a.priority !== undefined) return -1
    if (b.priority !== undefined) return 1
    return new Date(a.createdAt || 0) - new Date(b.createdAt || 0)
  })
})

// 首页显示的目标（限制数量）
const displayedGoals = computed(() => {
  return activeGoals.value.slice(0, maxDisplayGoals.value)
})

// 总的活跃目标数量
const totalActiveGoals = computed(() => {
  return activeGoals.value.length
})

// 兼容旧的currentGoal计算属性（保持向后兼容）
const currentGoal = computed(() => {
  const daily = activeGoals.value.find(goal => goal.type === 'daily')
  const weekly = activeGoals.value.find(goal => goal.type === 'weekly')
  
  return {
    dailyTarget: daily?.targetValue || 2000,
    dailyWritten: daily?.currentValue || 0,
    weeklyTarget: weekly?.targetValue || 14000,
    weeklyWritten: weekly?.currentValue || 0,
    streak: 0
  }
})

const recentNovels = computed(() => {
  // 按更新时间排序，取前3个
  return novelsData.value
    .sort((a, b) => new Date(b.updatedAt || 0) - new Date(a.updatedAt || 0))
    .slice(0, 3)
    .map(novel => ({
      id: novel.id,
      title: novel.title,
      description: novel.description,
      wordCount: novel.wordCount || 0,
      updatedAt: new Date(novel.updatedAt || Date.now()),
      cover: novel.cover
    }))
})

// 计算属性
const dailyProgress = computed(() => {
  return Math.min(100, Math.round((currentGoal.value.dailyWritten / currentGoal.value.dailyTarget) * 100))
})

const weeklyProgress = computed(() => {
  return Math.min(100, Math.round((currentGoal.value.weeklyWritten / currentGoal.value.weeklyTarget) * 100))
})

// 新增辅助函数
const getGoalProgress = (goal) => {
  if (!goal.targetValue || goal.targetValue === 0) return 0
  return Math.min(100, Math.round((goal.currentValue / goal.targetValue) * 100))
}

const calculateStreak = () => {
  // 简化的连续天数计算逻辑
  // 可以根据实际需求实现更复杂的逻辑
  return 0
}

const getGoalTypeText = (type) => {
  const typeMap = {
    daily: '每日',
    weekly: '每周', 
    monthly: '每月',
    custom: '自定义'
  }
  return typeMap[type] || '目标'
}

// 方法
const loadNovelsData = async () => {
  try {
    isLoading.value = true
    // 从后端API加载小说数据
    const response = await novelApi.getNovels()
    console.log('后端响应数据:', response)

    // 处理后端响应数据结构
    let novelsList = []
    if (response && response.data) {
      // 如果是标准的 Result 格式 {code, message, data}
      if (response.data.records && Array.isArray(response.data.records)) {
        // 分页格式 {records: [...], total: ..., current: ...}
        novelsList = response.data.records
      } else if (Array.isArray(response.data)) {
        // 直接数组格式
        novelsList = response.data
      } else {
        console.warn('未知的响应数据格式:', response)
        novelsList = []
      }
    } else if (response && response.records && Array.isArray(response.records)) {
      // 直接分页格式
      novelsList = response.records
    } else if (response && Array.isArray(response)) {
      // 直接数组格式
      novelsList = response
    } else {
      console.warn('未知的响应数据格式:', response)
      novelsList = []
    }

    // 确保 novelsList 是数组
    if (!Array.isArray(novelsList)) {
      console.warn('novelsList 不是数组:', novelsList)
      novelsList = []
    }

    novelsData.value = novelsList.map(novel => ({
      ...novel,
      createdAt: new Date(novel.createdAt),
      updatedAt: new Date(novel.updatedAt)
    }))

    console.log('首页从后端加载小说数据:', novelsData.value.length, '部小说')
  } catch (error) {
    console.error('首页加载小说数据失败:', error)
    // 如果后端加载失败，尝试从localStorage加载作为备用
    try {
      const saved = localStorage.getItem('novels')
      if (saved) {
        const parsedNovels = JSON.parse(saved)
        // 确保 parsedNovels 是数组
        if (Array.isArray(parsedNovels)) {
          novelsData.value = parsedNovels.map(novel => ({
            ...novel,
            createdAt: new Date(novel.createdAt),
            updatedAt: new Date(novel.updatedAt)
          }))
          console.log('首页使用本地缓存数据')
        } else {
          console.warn('本地缓存数据不是数组格式')
          novelsData.value = []
        }
      }
    } catch (localError) {
      console.error('本地数据加载也失败:', localError)
      novelsData.value = []
    }
  } finally {
    isLoading.value = false
  }
}

const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

const formatTime = (date) => {
  const now = new Date()
  const diff = now - date
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else {
    return '刚刚'
  }
}

const getProgressColor = (percentage) => {
  if (percentage >= 100) return '#67c23a'
  if (percentage >= 80) return '#e6a23c'
  if (percentage >= 60) return '#409eff'
  return '#f56c6c'
}

// 新增苹果风格进度条样式类
const getProgressClass = (percentage) => {
  if (percentage >= 100) return 'progress-complete'
  if (percentage >= 80) return 'progress-high'
  if (percentage >= 60) return 'progress-medium'
  return 'progress-low'
}

const createNovel = () => {
  router.push('/novels')
}

const openNovel = (novel) => {
  // 跳转到小说编辑页面
  router.push(`/writer?novelId=${novel.id}`)
}

const viewAllNovels = () => {
  router.push('/novels')
}

const openPrompts = () => {
  router.push('/prompts')
}

const openChapters = () => {
  router.push('/chapters')
}

const openBilling = () => {
  router.push('/billing')
}

// 页面获得焦点时重新计算数据，确保数据同步
const refreshData = async () => {
  goalsRefreshTrigger.value++
  await loadNovelsData()
  console.log('首页刷新数据')
}

// 暴露刷新函数给全局，以便其他页面调用
window.refreshHomeData = refreshData

// 生命周期
onMounted(async () => {
  // 初始加载数据
  await loadNovelsData()

  // 监听localStorage变化，以便实时更新目标数据
  window.addEventListener('storage', (e) => {
    if (e.key === 'writingGoals') {
      refreshData()
    }
  })

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      refreshData()
    }
  })
})
</script>

<style scoped>
/* 苹果风格全局样式 */
.apple-home-page {
  padding: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* 苹果风格Hero区域 */
.apple-hero-section {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  margin: 0 0 32px 0;
  padding: 48px 40px;
  color: white;
  overflow: hidden;
}

.hero-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-text {
  flex: 1;
}

.hero-title {
  font-size: 42px;
  font-weight: 700;
  margin: 0 0 12px 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
  line-height: 1.4;
}

.hero-action {
  margin-left: 40px;
}

/* 苹果风格按钮 */
.apple-primary-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 16px 32px;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  gap: 8px;
}

.apple-primary-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.apple-secondary-button {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #1d1d1f;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.apple-secondary-button:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.apple-text-button {
  background: none;
  border: none;
  color: #007aff;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.apple-text-button:hover {
  opacity: 0.7;
}

.apple-link-button {
  background: none;
  border: none;
  color: #007aff;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.apple-link-button:hover {
  opacity: 0.7;
}

/* 浮动装饰元素 */
.hero-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  font-size: 24px;
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.element-1 {
  top: 20%;
  right: 15%;
  animation-delay: 0s;
}

.element-2 {
  top: 60%;
  right: 25%;
  animation-delay: 2s;
}

.element-3 {
  top: 40%;
  right: 5%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

/* 苹果风格统计卡片 */
.apple-stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
}

.apple-stat-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.apple-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.stat-icon-wrapper {
  flex-shrink: 0;
}

.apple-stat-card .stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  background: linear-gradient(135deg, #007aff, #5856d6);
}

.novels-card .stat-icon {
  background: linear-gradient(135deg, #007aff, #5856d6);
}

.words-card .stat-icon {
  background: linear-gradient(135deg, #ff3b30, #ff9500);
}

.chapters-card .stat-icon {
  background: linear-gradient(135deg, #34c759, #30d158);
}

.tokens-card .stat-icon {
  background: linear-gradient(135deg, #ff9500, #ffcc02);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1d1d1f;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8e8e93;
  font-weight: 500;
}

.stat-skeleton {
  width: 60px;
  height: 28px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 苹果风格主要内容区域 */
.apple-main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

/* 苹果风格卡片 */
.apple-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.apple-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
}

.header-title {
  font-size: 18px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0;
}

/* 写作目标样式 */
.goals-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.apple-goal-item {
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.2s ease;
}

.apple-goal-item:hover {
  background: rgba(0, 0, 0, 0.04);
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.goal-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
}

.goal-target {
  font-size: 14px;
  font-weight: 600;
  color: #8e8e93;
}

.goal-progress-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.apple-progress-bar {
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.progress-complete {
  background: linear-gradient(90deg, #34c759, #30d158);
}

.progress-high {
  background: linear-gradient(90deg, #ff9500, #ffcc02);
}

.progress-medium {
  background: linear-gradient(90deg, #007aff, #5856d6);
}

.progress-low {
  background: linear-gradient(90deg, #ff3b30, #ff9500);
}

.progress-text {
  font-size: 12px;
  color: #8e8e93;
  font-weight: 500;
  text-align: right;
}

/* 空状态样式 */
.apple-empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  color: #8e8e93;
  margin: 0 0 20px 0;
}

/* 连续写作信息 */
.streak-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: rgba(255, 204, 0, 0.1);
  border: 1px solid rgba(255, 204, 0, 0.2);
  border-radius: 12px;
  margin-top: 16px;
}

.streak-icon {
  font-size: 18px;
}

.streak-text {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
}

/* 查看全部目标 */
.view-all-goals {
  text-align: center;
  padding: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 16px;
}

/* 快速操作网格 */
.apple-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.apple-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px 16px;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
}

.apple-action-item:hover {
  background: rgba(0, 122, 255, 0.1);
  border-color: rgba(0, 122, 255, 0.2);
  transform: translateY(-2px);
}

.action-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #007aff, #5856d6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.action-icon {
  font-size: 20px;
}

.action-label {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
}

/* 苹果风格小说列表 */
.apple-novels-section {
  margin-bottom: 32px;
}

.apple-novels-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.apple-novel-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.apple-novel-item:hover {
  background: rgba(0, 122, 255, 0.05);
  border-color: rgba(0, 122, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.novel-cover-wrapper {
  flex-shrink: 0;
}

.novel-cover {
  width: 64px;
  height: 80px;
  border-radius: 12px;
  object-fit: cover;
}

.default-novel-cover {
  width: 64px;
  height: 80px;
  background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8e8e93;
}

.cover-icon {
  font-size: 24px;
}

.novel-content {
  flex: 1;
  min-width: 0;
}

.apple-novel-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1d1d1f;
  line-height: 1.2;
}

.apple-novel-desc {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #8e8e93;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.apple-novel-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #8e8e93;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.meta-icon {
  font-size: 12px;
}

.novel-action {
  flex-shrink: 0;
}

.apple-continue-button {
  background: rgba(0, 122, 255, 0.1);
  border: 1px solid rgba(0, 122, 255, 0.2);
  color: #007aff;
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.apple-continue-button:hover {
  background: rgba(0, 122, 255, 0.2);
  transform: translateX(4px);
}

.button-arrow {
  transition: transform 0.3s ease;
}

.apple-continue-button:hover .button-arrow {
  transform: translateX(4px);
}

/* 空状态小说 */
.apple-empty-novels {
  text-align: center;
  padding: 60px 20px;
}

.empty-illustration {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-title {
  font-size: 20px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 16px;
  color: #8e8e93;
  margin: 0 0 24px 0;
  line-height: 1.4;
}

/* 加载状态 */
.apple-loading-state {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.loading-skeleton {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 16px;
}

.skeleton-cover {
  width: 64px;
  height: 80px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 12px;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-title {
  height: 20px;
  width: 60%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-desc {
  height: 14px;
  width: 80%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-meta {
  height: 12px;
  width: 40%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

/* 苹果风格响应式设计 */
@media (max-width: 1024px) {
  .apple-main-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .apple-home-page {
    padding: 0 16px;
  }

  .apple-hero-section {
    padding: 32px 24px;
    margin-bottom: 24px;
  }

  .hero-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .hero-action {
    margin-left: 0;
  }

  .hero-title {
    font-size: 32px;
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .apple-stat-card {
    padding: 20px;
  }

  .apple-card {
    padding: 20px;
  }

  .apple-actions-grid {
    grid-template-columns: 1fr;
  }

  .apple-novel-item {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .novel-content {
    text-align: center;
  }

  .apple-novel-meta {
    justify-content: center;
  }

  .floating-element {
    display: none;
  }
}

@media (max-width: 480px) {
  .apple-hero-section {
    padding: 24px 20px;
  }

  .hero-title {
    font-size: 28px;
  }

  .apple-primary-button {
    padding: 14px 24px;
    font-size: 14px;
  }

  .apple-stat-card {
    padding: 16px;
  }

  .stat-number {
    font-size: 24px;
  }

  .apple-card {
    padding: 16px;
  }

  .header-title {
    font-size: 16px;
  }

  .apple-novel-item {
    padding: 16px;
  }

  .apple-novel-title {
    font-size: 16px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .apple-home-page {
    background: linear-gradient(135deg, #1c1c1e 0%, #2c2c2e 100%);
  }

  .apple-card {
    background: rgba(28, 28, 30, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .header-title,
  .goal-title,
  .apple-novel-title,
  .stat-number {
    color: #ffffff;
  }

  .stat-label,
  .goal-target,
  .progress-text,
  .apple-novel-desc,
  .meta-item {
    color: #8e8e93;
  }

  .apple-goal-item,
  .apple-action-item,
  .apple-novel-item {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .apple-goal-item:hover,
  .apple-action-item:hover,
  .apple-novel-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}
</style>